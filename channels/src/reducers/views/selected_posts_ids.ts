import { SET_SELECTED_POSTS_IDS, CLEAR_SELECTED_POSTS_IDS } from "mattermost-redux/constants/selected_posts_ids";

type State = string[];

export default function selectedPostsIds(state: State = [], action: any): State {
    switch (action.type) {
        case SET_SELECTED_POSTS_IDS:
            return action.data;
        case CLEAR_SELECTED_POSTS_IDS:
            return [];
        default:
            return state;
    }
}