// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useState, useRef, useCallback, useEffect, useMemo} from 'react';
import {useDispatch, useSelector} from 'react-redux';

import {GenericModal} from '@mattermost/components';
import type {Channel} from '@mattermost/types/channels';

import {Client4} from 'mattermost-redux/client';
import {getCurrentTeamId} from 'mattermost-redux/selectors/entities/teams';
import {getCurrentUserId} from 'mattermost-redux/selectors/entities/users';

import {closeModal} from 'actions/views/modals';

import AlertBanner from 'components/alert_banner';
import LoadingScreen from 'components/loading_screen';
import RootPortal from 'components/root_portal';

import {Constants, ModalIdentifiers} from 'utils/constants';

import type {GlobalState} from 'types/store';

import './custom_user_status.scss';
// eslint-disable-next-line import/order
import {FaExpand} from 'react-icons/fa6';

import CustomUserStatusColorPicker from './custom_user_status_color_picker';
import CustomUserStatusVisibilityMenu from './custom_user_status_visibility_menu';
import MediaEditor from './media_editor';

import {clipVideo} from 'utils/video';

import {useIntl} from 'react-intl';

import LoadingSpinner from 'components/widgets/loading/loading_spinner';

type Props = {
    onExited: () => void;
};

const CustomUserStatusModal: React.FC<Props> = (props: Props) => {
    const intl = useIntl();

    const statusVisibilityText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.status_visibility',
        defaultMessage: 'The channel members who can see your story',
    });

    const unknownErrorErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.unknown_error',
        defaultMessage: 'An error occurred during publishing, please try again!',
    });

    const unSupportedFileErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.unsupported_file_error',
        defaultMessage: 'An error occurred, please make sure this file is valid.',
    });

    const emptyStatusErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.emtpy_status_error',
        defaultMessage: 'Nothing to post!',
    });

    const clipVideoErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.clip_video_error',
        defaultMessage: 'An error occurred while trimming the video. Please try again later.',
    });

    const noFileErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.no_file_error',
        defaultMessage: 'No file was selected!',
    });

    const fetchChannelsErrorMsg = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.fetch_channels_error',
        defaultMessage: 'An error occurred while fetching the channels!',
    });

    const modalHeaderText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.modal_header_text',
        defaultMessage: 'Story.',
    });

    const confirmButtonText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.confirm_button_text',
        defaultMessage: 'Upload story',
    });

    const cancelButtonText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.cancel_button_text',
        defaultMessage: 'Cancel',
    });

    const statusTextAreaPlaceholderText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.status_textarea_placeholder_text',
        defaultMessage: 'Add your story',
    });

    const uploadFileButtonText = intl.formatMessage({
        id: 'custom_user_status_modal.status_upload.upload_file_button_text',
        defaultMessage: 'Upload File',
    });

    const maxTextLength = 2200;

    const [file, setFile] = useState<File | null>(null);
    const [wasVideoFileEdited, setWasVideoFileEdited] = useState(false);
    const [statusText, setStatusText] = useState<string>('');
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [isMediaEditMode, setIsMediaEditMode] = useState(false);
    const [openChannels, setOpenChannels] = useState<Channel[]>([]);
    const [isLoadingChannels, setIsLoadingChannels] = useState(true);
    const [maxTextLengthColorIndicator, setMaxTextLengthColorIndicator] = useState('#777777');

    const fileURL = useMemo(() => {
        return file ? URL.createObjectURL(file) : '';
    }, [file]);

    const currentTeamId = useSelector(getCurrentTeamId);
    const [currentChannelIndex, setCurrentChannelIndex] = useState(0);

    const imageEditorRef = useRef(null);

    const textAreaRef = useRef<HTMLTextAreaElement | null>(null);

    const dispatch = useDispatch();
    const currentUserId = useSelector((state: GlobalState) => getCurrentUserId(state));

    const colorPalette = [
        '#000000',
        '#2C2C2C',
        '#1B2A41',
        '#1E3D2F',
        '#4B1D1D',
        '#3E4C59',
        '#2B1D3A',
        '#3D2C41',
        '#263238',
        '#37474F',
        '#212121',
        '#7B3F00',
        '#004953',
        '#3B7A57',
        '#800000',
        '#556B2F',
        '#1C1C1C',
    ];

    const [activeColor, setActiveColor] = useState(colorPalette[0]);

    const handleCloaseModal = useCallback(() => {
        dispatch(closeModal(ModalIdentifiers.CUSTOM_USER_STATUS));
    }, [dispatch]);

    const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files ? event.target.files[0] : null;

        if (selectedFile) {
            const fileType = selectedFile.type.startsWith('video') ? 'video' : selectedFile.type.startsWith('image') ? 'image' : '';

            if (!fileType) {
                setError(unSupportedFileErrorMsg);
                return;
            }

            setFile(selectedFile);
            if (selectedFile.type.startsWith('video') && wasVideoFileEdited) {
                setWasVideoFileEdited(false); // reset when uploading a file again
            }
        } else {
            setError(noFileErrorMsg);
        }
    }, []);

    const getEditedImageFile = async () => {
        if (imageEditorRef.current) {
            const editorInstance = imageEditorRef.current.getInstance();
            const dataURL = editorInstance.toDataURL();

            const response = await fetch(dataURL);
            const blob = await response.blob();

            const editedFile = new File([blob], file.name, {
                type: blob.type,
                lastModified: new Date().getTime(),
            });

            setFile(editedFile);
        }
    };

    const handleConfirm = async () => {
        if (currentUserId) {
            setIsLoading(true);
            setError(null);
            const channelId = openChannels[currentChannelIndex].id;
            try {
                if (file) {
                    // eslint-disable-next-line no-nested-ternary
                    const fileType = file.type.startsWith('video') ? 'video' : file.type.startsWith('image') ? 'image' : '';

                    if (fileType) {
                        let updatedVideoFile = file;
                        if (fileType === 'video') {
                            if (!wasVideoFileEdited) {
                                const result = await clipVideo(file, 0, 30); // clip the video if it was not edited by the user manually
                                if (!result.success) {
                                    setError(clipVideoErrorMsg);
                                    return;
                                }

                                updatedVideoFile = result.file;
                            }

                            // await Client4.uploadFileInChunks(updatedVideoFile, channelId, currentUserId);
                        } else if (fileType === 'image') {
                            await Client4.postUserStatus(currentUserId, fileType, file, statusText, channelId);
                            // eslint-disable-next-line no-console
                        }

                        handleCloaseModal();
                    } else {
                        setError(unSupportedFileErrorMsg);
                    }
                } else if (statusText) {
                    await Client4.postUserStatus(
                        currentUserId,
                        'text',
                        new File([''], 'dummy.txt', {type: 'application/octet-stream'}),
                        statusText,
                        channelId,
                        activeColor,
                    );
                    handleCloaseModal();
                } else {
                    setError(emptyStatusErrorMsg);
                }
            } catch (error) {
                // eslint-disable-next-line no-console
                console.error('Error uploading user status:', error);
                setError(unknownErrorErrorMsg);
            } finally {
                setIsLoading(false);
            }
        }
    };

    useEffect(() => {
        if (textAreaRef.current) {
            textAreaRef.current.focus();
        }

        const fetchAllChannels = async () => {
            try {
                const filteredChannels: Channel[] = [];
                let townSquareIndex = -1;
                const channels = await Client4.getAllTeamsChannels();

                channels.forEach((channel) => {
                    if (channel.type === Constants.OPEN_CHANNEL) {
                        filteredChannels.push(channel);
                        if (channel.team_id === currentTeamId && channel.name === 'town-square') {
                            townSquareIndex = filteredChannels.length - 1;
                        }
                    }
                });

                if (townSquareIndex !== -1) {
                    setCurrentChannelIndex(townSquareIndex);
                }

                setOpenChannels(filteredChannels);
                setIsLoadingChannels(false);
            } catch (error) {
                console.error('Error while fetching the channels ', error);
                setError(fetchChannelsErrorMsg);
            }
        };

        fetchAllChannels();
    }, []);

    const updateUserTextStatusInput = (value: string) => { // limit the text input length
        setStatusText(() => {
            const newText = value.slice(0, maxTextLength);
            setMaxTextLengthColorIndicator(
                newText.length === maxTextLength ? '#F44336' : newText.length >= maxTextLength / 2 ? '#FFC107' : '#777777',
            );

            return newText;
        });
    };

    const onSave = (editedFile?: File) => {
        if (!file) {
            return;
        }

        if (file.type.startsWith('image')) {
            getEditedImageFile();
        } else if (file.type.startsWith('video') && editedFile) {
            setFile(editedFile);
            setWasVideoFileEdited(true);
        }

        setIsMediaEditMode(false);
    };

    const mediaEditButton = (
        <button
            id='media-edit-btn'
            className='mb-2'
            onClick={() => setIsMediaEditMode(true)}
        >
            <FaExpand size={12}/>
            <span>وضع التحرير</span>
        </button>
    );

    return (
        <GenericModal
            compassDesign={true}
            modalHeaderText={modalHeaderText}
            onExited={props.onExited}
            confirmButtonText={confirmButtonText}
            cancelButtonText={cancelButtonText}
            handleConfirm={handleConfirm}
            handleEnterKeyPress={() => {}}
            handleCancel={() => {}}
            autoCloseOnConfirmButton={false}
            confirmButtonClassName='btn btn-primary'
            id='custom_user_status_modal'
        >
            <div className='modal-content-wrapper'>
                {isLoading && (
                    <LoadingScreen
                        className='loading-overlay'
                        position='fixed'
                    />
                )}
                {error && (
                    <AlertBanner
                        mode='danger'
                        message={error}
                        className='mb-4 align-items-center'
                        onDismiss={() => setError(null)}
                    />
                )}
                <div>
                    <div className='d-flex mb-2'>
                        <span> {statusVisibilityText}: </span>
                        {isLoadingChannels ? (
                            <LoadingSpinner style={{margin: '0 4px'}}/>
                        ) : (
                            <CustomUserStatusVisibilityMenu
                                openChannels={openChannels}
                                currentChannelIndex={currentChannelIndex}
                                setCurrentChannelIndex={setCurrentChannelIndex}
                            />
                        )}
                    </div>
                    {file && !isMediaEditMode ? (
                        <div className='mb-4 pos-relative'>
                            {mediaEditButton}
                            {file.type.startsWith('video') && (
                                <video
                                    autoPlay={true}
                                    onTimeUpdate={(e) => {
                                        // ensure the preview only displays the first 30s even if the video was not clipped
                                        const currentTime = e.currentTarget.currentTime;
                                        if (currentTime >= 30) {
                                            e.currentTarget.currentTime = 0;
                                        }
                                    }}
                                    src={fileURL}
                                    onClick={(e) => (e.currentTarget.paused ? e.currentTarget.play() : e.currentTarget.pause())}
                                    onEnded={(e) => e.currentTarget.play()}
                                />
                            )}
                            {file.type.startsWith('image') && (
                                <img src={fileURL}/>
                            )}
                        </div>
                    ) : file && isMediaEditMode && (
                        <RootPortal>
                            <MediaEditor
                                ref={imageEditorRef}
                                file={file}
                                onSave={onSave}
                                onCancel={() => setIsMediaEditMode(false)}
                            />
                        </RootPortal>
                    )}
                    <div>
                        <div
                            id='custom_user_status'
                            style={{background: !file ? activeColor : '', height: !file ? '280px' : ''}}
                        >
                            <textarea
                                id='custom_user_status__textarea'
                                ref={textAreaRef}
                                value={statusText}
                                onChange={(e) => updateUserTextStatusInput(e.target.value)}
                                onInput={(e) => {
                                    e.target.style.height = 'auto'; // Reset height
                                    e.target.style.height = `${e.target.scrollHeight}px`; // Set to scrollHeight
                                }}
                                placeholder={`${statusTextAreaPlaceholderText}...`}
                                className={`status-textarea${file ? '--media' : ''}`}
                            />
                        </div>

                        <span
                            className='d-block mb-2 mt-2'
                            style={{color: maxTextLengthColorIndicator}}
                        >
                            {statusText.length}/{maxTextLength}
                        </span>

                        {!file && (
                            <CustomUserStatusColorPicker
                                colorPalette={colorPalette}
                                activeColor={activeColor}
                                setActiveColor={setActiveColor}
                            />
                        )}
                    </div>
                    <button
                        className='p-0 btn btn-primary'
                    >
                        <label
                            htmlFor='file-upload'
                            className='px-4 p-2 pb-2 d-flex align-items-center mb-0 cursor--pointer'
                            style={{height: '100%'}}
                        >
                            {uploadFileButtonText}
                        </label>
                        <input
                            id='file-upload'
                            type='file'
                            accept='video/*,image/*'
                            onChange={handleFileChange}
                        />
                    </button>
                </div>
            </div>
        </GenericModal>
    );
};

export default CustomUserStatusModal;
