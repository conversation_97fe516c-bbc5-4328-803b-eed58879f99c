// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import classNames from 'classnames';
import React, {useCallback, useEffect, useRef, useState, useMemo} from 'react';
import type {MouseEvent} from 'react';
import {FormattedMessage} from 'react-intl';

import type {Emoji} from '@mattermost/types/emojis';
import type {FileInfo} from '@mattermost/types/files';
import type {Post} from '@mattermost/types/posts';
import type {Team} from '@mattermost/types/teams';
import type {UserProfile} from '@mattermost/types/users';

import {Posts} from 'mattermost-redux/constants/index';
import {getFile} from 'mattermost-redux/selectors/entities/files';
import {
    isMeMessage as checkIsMeMessage,
    isPostPendingOrFailed,
} from 'mattermost-redux/utils/post_utils';

import './custom.scss';
import {trackEvent} from 'actions/telemetry_actions';

import AutoHeightSwitcher, {AutoHeightSlots} from 'components/common/auto_height_switcher';
import EditPost from 'components/edit_post';
import FileAttachmentListContainer from 'components/file_attachment_list';
import MessageWithAdditionalContent from 'components/message_with_additional_content';
import PriorityLabel from 'components/post_priority/post_priority_label';
import PostProfilePicture from 'components/post_profile_picture';
import {VideoCallFromMobile} from 'components/post_videocall_mobile';
import PostAcknowledgements from 'components/post_view/acknowledgements';
import CommentedOn from 'components/post_view/commented_on/commented_on';
import DateSeparator from 'components/post_view/date_separator';
import FailedPostOptions from 'components/post_view/failed_post_options';
import PostAriaLabelDiv from 'components/post_view/post_aria_label_div';
import PostBodyAdditionalContent from 'components/post_view/post_body_additional_content';
import PostMessageContainer from 'components/post_view/post_message_view';
import PostPreHeader from 'components/post_view/post_pre_header';
import PostTime from 'components/post_view/post_time';
import ReactionList from 'components/post_view/reaction_list';
import ThreadFooter from 'components/threading/channel_threads/thread_footer';
import type {Props as TimestampProps} from 'components/timestamp/timestamp';
import ArchiveIcon from 'components/widgets/icons/archive_icon';

// eslint-disable-next-line import/order
import {TiArrowForwardOutline} from 'react-icons/ti';

// import InfoSmallIcon from 'components/widgets/icons/info_small_icon';
// import WithTooltip from 'components/with_tooltip';

import {getHistory} from 'utils/browser_history';
import Constants, {A11yCustomEventTypes, AppEvents, Locations} from 'utils/constants';
import type {A11yFocusEventDetail} from 'utils/constants';
import {isKeyPressed} from 'utils/keyboard';
import * as PostUtils from 'utils/post_utils';
import {getDateForUnixTicks, makeIsEligibleForClick} from 'utils/utils';

import type {GlobalState} from 'types/store';
import type {PostPluginComponent, PluginComponent} from 'types/store/plugins';

import PostOptions from './post_options';
import RepliedStatusToPreview from './replied-status-to-preview';
import RepliedPostToPreview from './replied_post_to_preview';
import PostUserProfile from './user_profile';

// eslint-disable-next-line import/order
import {useSelector} from 'react-redux';

export type Props = {
    post: Post;
    currentTeam?: Team;
    team?: Team;
    currentUserId: string;
    compactDisplay?: boolean;
    colorizeUsernames?: boolean;
    isFlagged: boolean;
    previewCollapsed?: string;
    previewEnabled?: boolean;
    isEmbedVisible?: boolean;
    enableEmojiPicker?: boolean;
    enablePostUsernameOverride?: boolean;
    isReadOnly?: boolean;
    pluginPostTypes?: { [postType: string]: PostPluginComponent };
    channelIsArchived?: boolean;
    isConsecutivePost?: boolean;
    isLastPost?: boolean;
    recentEmojis: Emoji[];
    center: boolean;
    handleCardClick?: (post: Post) => void;
    togglePostMenu?: (opened: boolean) => void;
    channelName?: string;
    displayName: string;
    teamDisplayName?: string;
    teamName?: string;
    channelType?: string;
    a11yIndex?: number;
    isBot: boolean;
    hasReplies: boolean;
    isFirstReply?: boolean;
    previousPostIsComment?: boolean;
    matches?: string[];
    term?: string;
    isMentionSearch?: boolean;
    location: keyof typeof Locations;
    actions: {
        markPostAsUnread: (post: Post, location: string) => void;
        emitShortcutReactToLastPostFrom: (emittedFrom: 'CENTER' | 'RHS_ROOT' | 'NO_WHERE') => void;
        selectPost: (post: Post) => void;
        selectPostFromRightHandSideSearch: (post: Post) => void;
        removePost: (post: Post) => void;
        closeRightHandSide: () => void;
        selectPostCard: (post: Post) => void;
        setRhsExpanded: (rhsExpanded: boolean) => void;
        toggleReplyBox: (method: 'open' | 'close', post?: Post) => void;
        setSelectedPostsIds: (postIds: string[]) => void;
    };
    timestampProps?: Partial<TimestampProps>;
    shouldHighlight?: boolean;
    isPostBeingEdited?: boolean;
    isCollapsedThreadsEnabled?: boolean;
    isMobileView: boolean;
    canReply?: boolean;
    replyCount?: number;
    isFlaggedPosts?: boolean;
    isPinnedPosts?: boolean;
    clickToReply?: boolean;
    isCommentMention?: boolean;
    parentPost?: Post;
    parentPostUser?: UserProfile | null;
    shortcutReactToLastPostEmittedFrom?: string;
    isPostAcknowledgementsEnabled: boolean;
    isPostPriorityEnabled: boolean;
    isCardOpen?: boolean;
    canDelete?: boolean;
    pluginActions: PluginComponent[];
    selectedPostsIds: string[];
};

const PostComponent = (props: Props): JSX.Element => {
    const {post, shouldHighlight, togglePostMenu, selectedPostsIds} = props;

    const postClickDelay = 170;

    const isSearchResultItem = (props.matches && props.matches.length > 0) || props.isMentionSearch || (props.term && props.term.length > 0);
    const isRHS = props.location === Locations.RHS_ROOT || props.location === Locations.RHS_COMMENT || props.location === Locations.SEARCH;
    const postRef = useRef<HTMLDivElement>(null);
    const postHeaderRef = useRef<HTMLDivElement>(null);
    const clickDebounceTimeout = useRef<ReturnType<typeof setTimeout> | null>(null);
    const teamId = props.team?.id ?? props.currentTeam?.id ?? '';

    const [hover, setHover] = useState(false);
    const [a11yActive, setA11y] = useState(false);
    const [dropdownOpened, setDropdownOpened] = useState(false);
    const [fileDropdownOpened, setFileDropdownOpened] = useState(false);
    const [fadeOutHighlight, setFadeOutHighlight] = useState(false);
    const [alt, setAlt] = useState(false);
    const [hasReceivedA11yFocus, setHasReceivedA11yFocus] = useState(false);
    const [isMenuOpened, setIsMenuOpened] = useState(false);

    const isSelected = selectedPostsIds.includes(post.id);

    const isSystemMessage = PostUtils.isSystemMessage(post);
    const fromAutoResponder = PostUtils.fromAutoResponder(post);

    const onToggleReplyBox = (method: 'open' | 'close', post?: Post) => {
        if (!isSystemMessage && props.actions.toggleReplyBox) {
            props.actions.toggleReplyBox(method, post);
        }
    };

    // eslint-disable-next-line max-statements-per-line
    const [fileInfo, setFileInfo] = useState<FileInfo | undefined>(); const getFileInfo = useSelector((state: GlobalState) => {
        if (post.file_ids && post.file_ids.length > 0) {
            return getFile(state, post.file_ids[0]);
        }
        return undefined;
    });

    const clearClickDebounceTimeout = () => {
        if (clickDebounceTimeout.current) {
            clearTimeout(clickDebounceTimeout.current);
            clickDebounceTimeout.current = null;
        }
    };

    useEffect(() => {
        setFileInfo(getFileInfo);
    }, [post]);

    // const postComponentRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (shouldHighlight) {
            const timer = setTimeout(() => setFadeOutHighlight(true), Constants.PERMALINK_FADEOUT);
            return () => {
                clearTimeout(timer);
            };
        }
        return undefined;
    }, [shouldHighlight]);

    useEffect(() => {
        return () => clearClickDebounceTimeout();
    }, []);

    const handleA11yActivateEvent = () => setA11y(true);
    const handleA11yDeactivateEvent = () => setA11y(false);
    const handleAlt = (e: KeyboardEvent) => setAlt(e.altKey);

    const handleA11yKeyboardFocus = useCallback((e: KeyboardEvent) => {
        if (!hasReceivedA11yFocus && shouldHighlight && isKeyPressed(e, Constants.KeyCodes.TAB) && e.shiftKey) {
            e.preventDefault();
            e.stopPropagation();

            setHasReceivedA11yFocus(true);

            document.dispatchEvent(new CustomEvent<A11yFocusEventDetail>(
                A11yCustomEventTypes.FOCUS, {
                    detail: {
                        target: postRef.current,
                        keyboardOnly: true,
                    },
                },
            ));
        }
    }, [hasReceivedA11yFocus, shouldHighlight]);

    useEffect(() => {
        if (a11yActive) {
            postRef.current?.dispatchEvent(new Event(A11yCustomEventTypes.UPDATE));
        }
    }, [a11yActive]);

    useEffect(() => {
        let removeEventListener: (type: string, listener: EventListener) => void;

        if (postRef.current) {
            postRef.current.addEventListener(A11yCustomEventTypes.ACTIVATE, handleA11yActivateEvent);
            postRef.current.addEventListener(A11yCustomEventTypes.DEACTIVATE, handleA11yDeactivateEvent);
            removeEventListener = postRef.current.removeEventListener;
        }

        return () => {
            if (removeEventListener) {
                removeEventListener(A11yCustomEventTypes.ACTIVATE, handleA11yActivateEvent);
                removeEventListener(A11yCustomEventTypes.DEACTIVATE, handleA11yDeactivateEvent);
            }
        };
    }, []);

    useEffect(() => {
        if (hover) {
            document.addEventListener('keydown', handleAlt);
            document.addEventListener('keyup', handleAlt);
        }

        return () => {
            document.removeEventListener('keydown', handleAlt);
            document.removeEventListener('keyup', handleAlt);
        };
    }, [hover]);

    useEffect(() => {
        document.addEventListener('keyup', handleA11yKeyboardFocus);

        return () => {
            document.removeEventListener('keyup', handleA11yKeyboardFocus);
        };
    }, [handleA11yKeyboardFocus]);

    const hasSameRoot = (props: Props) => {
        if (props.isFirstReply) {
            return false;
        } else if (!post.root_id && !props.previousPostIsComment && props.isConsecutivePost) {
            return true;
        } else if (post.root_id) {
            return true;
        }
        return false;
    };

    const getChannelName = () => {
        let name: React.ReactNode = props.channelName;

        const isDirectMessage = props.channelType === Constants.DM_CHANNEL;
        const isPartOfThread = props.isCollapsedThreadsEnabled && (post.reply_count > 0 || post.is_following);

        if (isDirectMessage && isPartOfThread) {
            name = (
                <FormattedMessage
                    id='search_item.thread_direct'
                    defaultMessage='Thread in Direct Message (with {username})'
                    values={{
                        username: props.displayName,
                    }}
                />
            );
        } else if (isPartOfThread) {
            name = (
                <FormattedMessage
                    id='search_item.thread'
                    defaultMessage='Thread in {channel}'
                    values={{
                        channel: props.channelName,
                    }}
                />
            );
        } else if (isDirectMessage) {
            name = (
                <FormattedMessage
                    id='search_item.direct'
                    defaultMessage='Direct Message (with {username})'
                    values={{
                        username: props.displayName,
                    }}
                />
            );
        }

        return name;
    };

    const getPostHeaderVisible = (): boolean | null => {
        const boundingRectOfPostInfo: DOMRect | undefined = postHeaderRef.current?.getBoundingClientRect();

        let isPostHeaderVisibleToUser: boolean | null = null;
        if (boundingRectOfPostInfo) {
            isPostHeaderVisibleToUser = (boundingRectOfPostInfo.top - 65) > 0 &&
                boundingRectOfPostInfo.bottom < (window.innerHeight - 85);
        }

        return isPostHeaderVisibleToUser;
    };
    const hovered =
        hover || fileDropdownOpened || dropdownOpened || a11yActive || props.isPostBeingEdited;
    const getClassName = () => {
        const isMeMessage = checkIsMeMessage(post);
        const hovered =
            hover || fileDropdownOpened || dropdownOpened || a11yActive || props.isPostBeingEdited;
        return classNames('a11y__section post', {
            'post--highlight': shouldHighlight && !fadeOutHighlight,
            'same--root': hasSameRoot(props),
            'other--root': !hasSameRoot(props) && !isSystemMessage,
            'post--bot': PostUtils.isFromBot(post),
            'post--editing': props.isPostBeingEdited,
            'current--user': props.currentUserId === post.user_id && !isSystemMessage,
            'post--system': isSystemMessage || isMeMessage,
            'post--root': props.hasReplies && !(post.root_id && post.root_id.length > 0),
            'post--comment': (post.root_id && post.root_id.length > 0 && !props.isCollapsedThreadsEnabled) || (props.location === Locations.RHS_COMMENT),
            'post--compact': props.compactDisplay,
            'post--hovered': hovered,
            'same--user': props.isConsecutivePost && (!props.compactDisplay || props.location === Locations.RHS_COMMENT),
            'cursor--pointer': alt && !props.channelIsArchived,
            'post--hide-controls': post.failed || post.state === Posts.POST_DELETED,
            'post--comment same--root': fromAutoResponder,
            'post--pinned-or-flagged': (post.is_pinned || props.isFlagged) && props.location === Locations.CENTER,
            'mention-comment': props.isCommentMention,
            'post--thread': isRHS,
            'post--selected': isSelected,
        });
    };

    const handleFileDropdownOpened = useCallback((open: boolean) => setFileDropdownOpened(open), []);

    const handleDropdownOpened = useCallback((opened: boolean) => {
        if (togglePostMenu) {
            togglePostMenu(opened);
        }
        setDropdownOpened(opened);
    }, [togglePostMenu]);

    const handleMouseOver = useCallback((e: MouseEvent<HTMLDivElement>) => {
        setHover(true);
        setAlt(e.altKey);
    }, []);

    const handleMouseLeave = useCallback(() => {
        setHover(false);
        setAlt(false);
    }, []);

    // const handleCardClick = (post?: Post) => {
    //     if (!post) {
    //         return;
    //     }
    //     if (props.handleCardClick) {
    //         props.handleCardClick(post);
    //     }
    //     props.actions.selectPostCard(post);
    // };

    // When adding clickable targets within a root post to exclude from post's on click to open thread,
    // please add to/maintain the selector below
    const isEligibleForClick = useMemo(() => makeIsEligibleForClick('.post-image__column, .embed-responsive-item, .attachment, .hljs, code'), []);

    const handleMultiSelect = (postId: string) => {
        let updatedSelectedPostsIds;
        if (selectedPostsIds.includes(postId)) {
            updatedSelectedPostsIds = selectedPostsIds.filter((id) => id !== postId);
        } else {
            updatedSelectedPostsIds = [...selectedPostsIds, postId];
        }

        props.actions.setSelectedPostsIds(updatedSelectedPostsIds);
    };

    const handlePostClick = useCallback((e: MouseEvent<HTMLDivElement>) => {
        if (!post || props.channelIsArchived) {
            return;
        }

        if (selectedPostsIds.length > 0) {
            handleMultiSelect(post.id);
        }

        if (
            !e.altKey &&
            props.clickToReply &&
            (fromAutoResponder || !isSystemMessage) &&
            isEligibleForClick(e) &&
            props.location === Locations.CENTER &&
            !props.isPostBeingEdited
        ) {
            clearClickDebounceTimeout();
            clickDebounceTimeout.current = setTimeout(() => {
                trackEvent('crt', 'clicked_to_reply');
                props.actions.selectPost(post);
            }, postClickDelay);
        }

        if (e.altKey) {
            props.actions.markPostAsUnread(post, props.location);
        }
    }, [
        post,
        fromAutoResponder,
        isEligibleForClick,
        isSystemMessage,
        props.channelIsArchived,
        props.clickToReply,
        props.actions,
        props.location,
        props.isPostBeingEdited,
        selectedPostsIds,
    ]);

    const handleDoublePostClick = () => {
        clearClickDebounceTimeout();

        onToggleReplyBox('open', post);
    };

    const handleJumpClick = useCallback((e: React.MouseEvent, targetPost = post) => {
        e.preventDefault();
        if (props.isMobileView) {
            props.actions.closeRightHandSide();
        }

        props.actions.setRhsExpanded(false);
        getHistory().push(`/${props.teamName}/pl/${targetPost.id}`);
    }, [props.isMobileView, props.actions, props.teamName, post?.id]);

    const {selectPostFromRightHandSideSearch} = props.actions;

    const handleCommentClick = useCallback((e: React.MouseEvent) => {
        e.preventDefault();

        if (!post) {
            return;
        }
        selectPostFromRightHandSideSearch(post);
    }, [post, selectPostFromRightHandSideSearch]);

    const handleThreadClick = useCallback((e: React.MouseEvent) => {
        if (props.currentTeam?.id === teamId) {
            handleCommentClick(e);
        } else {
            handleJumpClick(e);
        }
    }, [handleCommentClick, handleJumpClick, props.currentTeam?.id, teamId]);

    const postClass = classNames('post__body', {'post--edited': PostUtils.isEdited(post), 'search-item-snippet': isSearchResultItem});

    let comment;
    if (props.isFirstReply && props.parentPost && props.parentPostUser && post.type !== Constants.PostTypes.EPHEMERAL) {
        comment = (
            <CommentedOn
                post={props.parentPost}
                parentPostUser={props.parentPostUser}
                onCommentClick={handleCommentClick}
            />
        );
    }

    let visibleMessage = null;
    if (post.type === Constants.PostTypes.EPHEMERAL && !props.compactDisplay && post.state !== Posts.POST_DELETED) {
        visibleMessage = (
            <span className='post__visibility'>
                <FormattedMessage
                    id='post_info.message.visible'
                    defaultMessage='(Only visible to you)'
                />
            </span>
        );
    }

    let profilePic;
    const hideProfilePicture = hasSameRoot(props) && (!post.root_id && !props.hasReplies) && !PostUtils.isFromBot(post);
    const hideProfileCase = !(props.location === Locations.RHS_COMMENT && props.compactDisplay && props.isConsecutivePost);
    if (!hideProfilePicture && hideProfileCase) {
        profilePic = (
            <PostProfilePicture
                compactDisplay={props.compactDisplay}
                post={post}
                userId={post.user_id}
            />
        );

        if (fromAutoResponder) {
            profilePic = (
                <span className='auto-responder'>
                    {profilePic}
                </span>
            );
        }
    }
    const dropdownMenuButton = (
        selectedPostsIds.length === 0 ? (
            <button
                style={{
                    backgroundColor: 'inherit',
                    border: '0',
                }}
                onClick={() => setIsMenuOpened(!isMenuOpened)}
            >
                <svg
                    width='24'
                    height='25'
                    viewBox='0 0 24 25'
                    fill='#00987E'
                    xmlns='http://www.w3.org/2000/svg'
                >
                    <path
                        fillRule='evenodd'
                        clipRule='evenodd'
                        d='M12 2.75C6.61522 2.75 2.25 7.11522 2.25 12.5C2.25 17.8848 6.61522 22.25 12 22.25C17.3848 22.25 21.75 17.8848 21.75 12.5C21.75 7.11522 17.3848 2.75 12 2.75ZM12 11.375C11.3787 11.375 10.875 11.8787 10.875 12.5C10.875 13.1213 11.3787 13.625 12 13.625C12.6213 13.625 13.125 13.1213 13.125 12.5C13.125 11.8787 12.6213 11.375 12 11.375ZM15.375 12.5C15.375 11.8787 15.8787 11.375 16.5 11.375C17.1213 11.375 17.625 11.8787 17.625 12.5C17.625 13.1213 17.1213 13.625 16.5 13.625C15.8787 13.625 15.375 13.1213 15.375 12.5ZM7.5 11.375C6.87868 11.375 6.375 11.8787 6.375 12.5C6.375 13.1213 6.87868 13.625 7.5 13.625C8.12132 13.625 8.625 13.1213 8.625 12.5C8.625 11.8787 8.12132 11.375 7.5 11.375Z'
                    />
                </svg>
            </button>
        ) : null
    );

    // eslint-disable-next-line no-nested-ternary
    const message = isSearchResultItem ? (
        <PostBodyAdditionalContent
            post={post}
            options={{
                searchTerm: props.term,
                searchMatches: props.matches,
            }}
        >
            <PostMessageContainer
                post={post}
                options={{
                    searchTerm: props.term,
                    searchMatches: props.matches,
                    mentionHighlight: props.isMentionSearch,
                }}
                isRHS={isRHS}
            />
        </PostBodyAdditionalContent>
    ) : post.message.endsWith('role=meeting_link') ? (
        <VideoCallFromMobile
            link={post.message.replace('role=meeting_link', '')}
        />
    ) : (
        <MessageWithAdditionalContent
            post={post}
            isEmbedVisible={props.isEmbedVisible}
            pluginPostTypes={props.pluginPostTypes}
            isRHS={isRHS}
            compactDisplay={props.compactDisplay}
        />
    );

    const slotBasedOnEditOrMessageView = props.isPostBeingEdited ? AutoHeightSlots.SLOT2 : AutoHeightSlots.SLOT1;
    const threadFooter = props.location !== Locations.RHS_ROOT && props.isCollapsedThreadsEnabled && !post.root_id && (props.hasReplies || post.is_following) ? (
        <ThreadFooter
            threadId={post.id}
            replyClick={handleThreadClick}
        />
    ) : null;
    const currentPostDay = getDateForUnixTicks(post.create_at);
    const channelDisplayName = getChannelName();
    const showReactions = props.location !== Locations.SEARCH || props.isPinnedPosts || props.isFlaggedPosts;

    const getTestId = () => {
        let idPrefix: string;
        switch (props.location) {
        case 'CENTER':
            idPrefix = 'post';
            break;
        case 'RHS_ROOT':
        case 'RHS_COMMENT':
            idPrefix = 'rhsPost';
            break;
        case 'SEARCH':
            idPrefix = 'searchResult';
            break;

        default:
            idPrefix = 'post';
        }

        return idPrefix + `_${post.id}`;
    };

    let priority;
    if (post.metadata?.priority && props.isPostPriorityEnabled) {
        priority = <span className='d-flex mr-2 ml-1'><PriorityLabel priority={post.metadata.priority.priority}/></span>;
    }

    let postAriaLabelDivTestId = '';
    if (props.location === Locations.CENTER) {
        postAriaLabelDivTestId = 'postView';
    } else if (props.location === Locations.RHS_ROOT || props.location === Locations.RHS_COMMENT) {
        postAriaLabelDivTestId = 'rhsPostView';
    }

    const repliedToPost = post.props.repliedToPost as Post;
    const userStatusID = post.props.status_id;
    const userStatusText = post.props.text;
    const userStatusPreviewID = post.props.preview;
    const isPostForwarded = post.props.is_forwarded;

    const forwardedText = (
        <div
            className='d-flex mb-1'
            style={{
                fontStyle: 'italic',
                fontSize: '14px',
                opacity: '0.7',
                alignItems: 'center',
                gap: '4px',
            }}
        >
            <TiArrowForwardOutline/>
            <FormattedMessage
                id='post_props.props.forwarded'
                defaultMessage='Forwarded'
            />
        </div>
    );

    return (
        <>
            {(isSearchResultItem || (props.location !== Locations.CENTER && (props.isPinnedPosts || props.isFlaggedPosts))) && <DateSeparator date={currentPostDay}/>}
            <PostAriaLabelDiv
                ref={postRef}
                id={getTestId()}
                data-testid={postAriaLabelDivTestId}
                tabIndex={0}
                post={post}
                className={getClassName()}
                onClick={handlePostClick}
                onDoubleClick={handleDoublePostClick}
                onMouseOver={handleMouseOver}
                onMouseLeave={handleMouseLeave}
            >
                {(Boolean(isSearchResultItem) || (props.location !== Locations.CENTER && props.isFlagged)) &&
                    <div
                        className='search-channel__name__container'
                        aria-hidden='true'
                    >
                        {(Boolean(isSearchResultItem) || props.isFlaggedPosts) &&
                            <span className='search-channel__name'>
                                {channelDisplayName}
                            </span>
                        }
                        {props.channelIsArchived &&
                            <span className='search-channel__archived'>
                                <ArchiveIcon className='icon icon__archive channel-header-archived-icon svg-text-color'/>
                                <FormattedMessage
                                    id='search_item.channelArchived'
                                    defaultMessage='Archived'
                                />
                            </span>
                        }
                        {(Boolean(isSearchResultItem) || props.isFlaggedPosts) && Boolean(props.teamDisplayName) &&
                            <span className='search-team__name'>
                                {props.teamDisplayName}
                            </span>
                        }
                    </div>
                }
                <PostPreHeader
                    isFlagged={props.isFlagged}
                    isPinned={post.is_pinned}
                    skipPinned={props.location === Locations.SEARCH && props.isPinnedPosts}
                    skipFlagged={props.location === Locations.SEARCH && props.isFlaggedPosts}
                    channelId={post.channel_id}
                />
                <div
                    role='application'
                    className={`post__content ${props.center ? 'center' : ''} ${props.currentUserId === post.user_id ? 'post--left' : 'post--right'
                    }`}
                    data-testid='postContent'
                >
                    <div className='post__img'>
                        {profilePic}
                    </div>
                    <div>
                        <div
                            className='post__header'
                            ref={postHeaderRef}
                        >
                            <PostUserProfile
                                {...props}
                                isSystemMessage={isSystemMessage}
                            />
                            <div className='col d-flex align-items-center'>
                                {!props.isConsecutivePost && ((!hideProfilePicture && props.location === Locations.CENTER) || hover || props.location !== Locations.CENTER) &&
                                    <PostTime
                                        isPermalink={!(Posts.POST_DELETED === post.state || isPostPendingOrFailed(post))}
                                        teamName={props.team?.name}
                                        eventTime={post.create_at}
                                        postId={post.id}
                                        location={props.location}
                                        timestampProps={{...props.timestampProps, style: props.isConsecutivePost && !props.compactDisplay ? 'narrow' : undefined}}
                                    />
                                }
                                {priority}
                                {/* {post.props && post.props.card &&
                                    <WithTooltip
                                        title={
                                            <FormattedMessage
                                                id='post_info.info.view_additional_info'
                                                defaultMessage='View additional info'
                                            />
                                        }
                                    >
                                        <button
                                            className={'card-icon__container icon--show style--none ' + (props.isCardOpen ? 'active' : '')}
                                            onClick={(e) => {
                                                e.preventDefault();
                                                handleCardClick(post);
                                            }}
                                        >
                                            <InfoSmallIcon
                                                className='icon icon__info'
                                                aria-hidden='true'
                                            />
                                        </button>
                                    </WithTooltip>
                                } */}
                                {visibleMessage}
                            </div>

                        </div>
                        {comment}
                        <div
                            className={postClass}
                            id={isRHS ? undefined : `${post.id}_message`}
                        >
                            {post.failed && <FailedPostOptions post={post}/>}
                            <AutoHeightSwitcher
                                showSlot={slotBasedOnEditOrMessageView}
                                shouldScrollIntoView={props.isPostBeingEdited}
                                slot1={<>
                                    <div
                                        style={{display: 'flex', alignItems: 'center'}}
                                    >
                                        <div
                                            className={`${
                                                // eslint-disable-next-line no-nested-ternary
                                                post.message === 'BUZZMESSAGE' ?
                                                    'buzz-message' : // eslint-disable-next-line no-nested-ternary
                                                    props.currentUserId === post.user_id ? 'post--left1' : 'post--right1'
                                            } ${!props.isConsecutivePost && 'first-post'}`}
                                            style={{
                                                maxWidth: '100%',
                                                flexDirection: 'column',
                                                justifyContent: 'end',
                                            }}
                                        // eslint-disable-next-line react/jsx-closing-bracket-location
                                        >
                                            {isPostForwarded ? forwardedText : repliedToPost ? (
                                                <RepliedPostToPreview
                                                    repliedToPost={repliedToPost}
                                                    handleJumpClick={handleJumpClick}
                                                />
                                            ) : userStatusID && (
                                                <RepliedStatusToPreview
                                                    statusID={userStatusID}
                                                    statusText={userStatusText}
                                                    statusPreviewID={userStatusPreviewID}
                                                />
                                            )}

                                            {post.file_ids && post.file_ids.length > 0 &&
                                                <FileAttachmentListContainer
                                                    post={post}
                                                    compactDisplay={props.compactDisplay}
                                                    handleFileDropdownOpened={handleFileDropdownOpened}
                                                />
                                            }
                                            {message}
                                            <div
                                                className='d-flex pt-1'
                                                style={{gap: '4px', justifyContent: 'end'}}

                                            >
                                                {/* Adding keyframes animation */}
                                                {props.isConsecutivePost &&
                                                    // eslint-disable-next-line no-constant-binary-expression
                                                    ((!hideProfilePicture &&
                                                        props.location === Locations.CENTER) ||
                                                        true || // always show the time message in consecutive posts
                                                        props.location !== Locations.CENTER) && (
                                                        <PostTime
                                                        isPermalink={
                                                            !(
                                                                Posts.POST_DELETED === post.state ||
                                                                    isPostPendingOrFailed(post)
                                                            )
                                                        }
                                                        teamName={props.team?.name}
                                                        eventTime={post.create_at}
                                                        postId={post.id}
                                                        location={props.location}
                                                        timestampProps={{
                                                            ...props.timestampProps,
                                                            style:
                                                                    props.isConsecutivePost &&
                                                                        !props.compactDisplay ? 'narrow' : undefined,
                                                        }}
                                                    />
                                                )}
                                                <div className='post__body-reactions-acks'>

                                                    {post.message !== 'BUZZMESSAGE' && (post.props?.ack === true || post.props?.acknowledgements || post.metadata?.priority?.requested_ack) && (
                                                        <PostAcknowledgements
                                                            authorId={post.user_id}
                                                            isDeleted={post.state === Posts.POST_DELETED}
                                                            postId={post.id}
                                                        />
                                                    )}

                                                </div>

                                            </div>
                                        </div>
                                        {hovered && dropdownMenuButton}
                                        {!props.isPostBeingEdited && isMenuOpened && selectedPostsIds.length === 0 &&
                                            <PostOptions
                                                {...props}
                                                teamId={teamId}
                                                handleDropdownOpened={handleDropdownOpened}
                                                handleCommentClick={handleCommentClick}
                                                hover={hover || a11yActive}
                                                removePost={props.actions.removePost}
                                                handleJumpClick={handleJumpClick}
                                                isPostHeaderVisible={getPostHeaderVisible()}
                                                fileInfo={fileInfo}
                                                toggleReplyBox={onToggleReplyBox}
                                                handleMultiSelect={handleMultiSelect}
                                            />
                                        }
                                    </div>
                                    {showReactions && <ReactionList post={post}/>}</>}
                                slot2={<EditPost/>}
                                onTransitionEnd={() => document.dispatchEvent(new Event(AppEvents.FOCUS_EDIT_TEXTBOX))}
                            />

                            {threadFooter}

                        </div>
                    </div>
                </div>
            </PostAriaLabelDiv>
        </>
    );
};

export default PostComponent;
