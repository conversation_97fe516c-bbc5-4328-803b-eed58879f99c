import React, {useRef, useState} from 'react';
import {Modal} from 'react-bootstrap';
import {FormattedMessage} from 'react-intl';
import {useDispatch, useSelector} from 'react-redux';

import {deleteAndRemovePost} from 'actions/post_actions';
import {clearSelectedPostsIds} from 'actions/selected_posts_ids';
import {GlobalState} from 'types/store';

type Props = {
    onHide: () => void;
    onExited: () => void;
};

const DeleteMultiplePostsModal = ({onHide, onExited}: Props) => {
    const [deletePostsInProgress, setDeletePostsInProgress] = useState(false);
    const deleteMultiplePostsBtn = useRef<HTMLButtonElement>(null);
    const dispatch = useDispatch();

    const postsById = useSelector((state: GlobalState) => state.entities.posts.posts);
    const selectedPostsIds = useSelector((state: GlobalState) => state.selectedPostsIds);
    const getPost = (postId: string) => postsById[postId];

    const handleMultipleDelete = async () => {
        if (!selectedPostsIds || selectedPostsIds.length === 0) {
            onHide();
            return;
        }

        setDeletePostsInProgress(true);
        try {
            for (const postId of selectedPostsIds) {
                const post = getPost(postId);
                if (post) {
                    await dispatch(deleteAndRemovePost(post));
                }
            };

            dispatch(clearSelectedPostsIds());
        } catch (err) {
            console.error('Error while deleting post', err);
        } finally {
            setDeletePostsInProgress(false);
            onHide();
        }
    };

    const handleEntered = () => {
        deleteMultiplePostsBtn.current?.focus();
    };

    return (
        <Modal
            dialogClassName='a11y__modal'
            show={true}
            onEntered={handleEntered}
            onHide={onHide}
            onExited={onExited}
            id='deleteMultiplePostsModal'
            role='none'
            aria-labelledby='deleteMultiplePostsModalLabel'
        >
            <Modal.Header closeButton={true}>
                <Modal.Title componentClass='h1' id='deleteMultiplePostsModalLabel'>
                    <FormattedMessage id='delete_multiple_posts.confirm_post' defaultMessage='Confirm Deleting Posts' />
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <FormattedMessage
                    id='delete_multiple_posts.question_post'
                    defaultMessage='Are you sure you want to delete these messages?'
                    tagName='p'
                />
            </Modal.Body>
            <Modal.Footer>
                <button type='button' className='btn btn-tertiary' onClick={onHide} disabled={deletePostsInProgress}>
                    <FormattedMessage id='delete_multiple_posts.cancel' defaultMessage='Cancel' />
                </button>
                <button
                    ref={deleteMultiplePostsBtn}
                    type='button'
                    autoFocus={true}
                    className='btn btn-danger'
                    onClick={handleMultipleDelete}
                    id='deleteMultiplePostsModalButton'
                    disabled={deletePostsInProgress}
                >
                    <FormattedMessage id='delete_multiple_posts.del' defaultMessage='Delete' />
                </button>
            </Modal.Footer>
        </Modal>
    );
};

export default DeleteMultiplePostsModal;
